# 汇率历史清理设计文档

## 概述

本文档描述了汇率更新后只保留最新 effectiveDate 汇率的设计和实现。

## 背景

在汇率管理系统中，用户可能会多次更新同一货币对的汇率，导致数据库中存储了多个不同 effectiveDate 的汇率记录。为了优化存储空间和查询性能，我们需要在汇率更新后自动清理历史记录，只保留最新的汇率。

## 设计原则

1. **默认行为**：汇率历史清理是默认启用的行为，不需要用户配置
2. **数据一致性**：确保清理过程不影响数据的完整性和一致性
3. **性能优化**：清理操作不应影响主要的汇率更新流程
4. **错误处理**：清理失败不应影响汇率更新的成功

## 实现方案

### 1. 核心服务

创建了 `exchange-rate-cleanup.service.ts` 服务，提供以下功能：

- `cleanupExchangeRateHistory()`: 清理用户所有货币对的历史汇率
- `cleanupSpecificCurrencyPairHistory()`: 清理特定货币对的历史汇率

### 2. 清理逻辑

对于每个货币对：
1. 按 `effectiveDate` 降序排序所有汇率记录
2. 保留最新的记录（第一条）
3. 删除所有历史记录（其余记录）

### 3. 集成点

汇率历史清理集成到以下场景：

#### 3.1 手动汇率更新 (POST /api/exchange-rates)
- 在单个汇率创建/更新完成后
- 在自动汇率生成完成后
- 在缓存清除之前

#### 3.2 批量汇率更新 (PUT /api/exchange-rates)
- 在所有汇率批量更新完成后
- 在自动汇率生成完成后
- 在缓存清除之前

#### 3.3 自动汇率更新 (ExchangeRateAutoUpdateService)
- 在API汇率更新完成后
- 在自动汇率生成完成后
- 在返回结果之前

#### 3.4 单个汇率更新 (PUT /api/exchange-rates/[id])
- 在汇率更新完成后
- 在自动汇率生成完成后
- 在返回结果之前

### 4. 缓存处理

- 清理操作默认不立即清除缓存（`clearCache: false`）
- 由调用方统一处理缓存清除，避免重复操作
- 使用现有的 `revalidateExchangeRateCache()` 函数

## 代码示例

### 基本用法

```typescript
// 清理特定货币对的历史汇率
await cleanupSpecificCurrencyPairHistory(
  userId,
  fromCurrencyId,
  toCurrencyId,
  { clearCache: false }
)

// 清理用户所有货币对的历史汇率
await cleanupExchangeRateHistory(userId, { clearCache: false })
```

### 错误处理

```typescript
try {
  await cleanupExchangeRateHistory(userId, { clearCache: false })
} catch (error) {
  console.error('清理汇率历史失败:', error)
  // 不影响主要操作，只记录错误
}
```

## 数据库影响

### 查询优化
- 减少了每个货币对的汇率记录数量
- 提高了汇率查询的性能
- 减少了存储空间占用

### 数据完整性
- 保留了最新的汇率数据
- 不影响现有的业务逻辑
- 维护了数据的一致性

## 测试

创建了完整的单元测试 (`exchange-rate-cleanup.test.ts`)，覆盖：
- 多条历史记录的清理
- 单条记录的处理
- 多个货币对的批量清理
- 错误情况的处理

## 性能考虑

1. **异步处理**：清理操作不阻塞主要的汇率更新流程
2. **批量操作**：使用数据库的批量删除操作
3. **缓存优化**：避免重复的缓存清除操作
4. **错误隔离**：清理失败不影响汇率更新的成功

## 监控和日志

- 记录清理操作的结果和统计信息
- 错误日志记录但不中断主流程
- 开发环境下提供详细的调试信息

## 未来扩展

如果需要，可以考虑以下扩展：
1. 添加配置选项控制清理行为
2. 支持保留指定数量的历史记录
3. 添加清理操作的审计日志
4. 支持按时间范围清理历史记录

## 总结

该设计实现了汇率更新后自动清理历史记录的需求，确保了数据的一致性和性能优化，同时保持了系统的稳定性和可维护性。
